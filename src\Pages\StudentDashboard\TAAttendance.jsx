import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON>,
  FiUserCheck,
  FiUserX,
  FiMapPin,
  FiSave,
  FiAlertCircle,
} from "react-icons/fi";
import { useAuth } from "../../contexts/AuthContext";

const TAAttendance = () => {
  const { user } = useAuth();
  const [availability, setAvailability] = useState({
    status: "available",
    location: "",
    availableFrom: "",
    availableTo: "",
  });
  const [doctorAvailability, setDoctorAvailability] = useState(null);

  // Load data from localStorage on component mount
  useEffect(() => {
    if (user?.role === "doctor") {
      // Load doctor's saved availability
      const savedAvailability = localStorage.getItem("doctorAvailability");
      if (savedAvailability) {
        setAvailability(JSON.parse(savedAvailability));
      }
    } else {
      // Load doctor's availability for student view
      const savedDoctorAvailability =
        localStorage.getItem("doctorAvailability");
      if (savedDoctorAvailability) {
        setDoctorAvailability(JSON.parse(savedDoctorAvailability));
      }
    }
  }, [user]);

  // Handle form input changes for doctor
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setAvailability((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle status toggle for doctor
  const handleStatusToggle = () => {
    setAvailability((prev) => ({
      ...prev,
      status: prev.status === "available" ? "not-available" : "available",
    }));
  };

  // Save availability data for doctor
  const handleSaveAvailability = () => {
    localStorage.setItem("doctorAvailability", JSON.stringify(availability));
    // Show success message (you can replace with a toast notification)
    alert("Availability updated successfully!");
  };

  // Get card and styling classes based on role
  const getCardClass = () => {
    return user?.role === "doctor" ? "doctor-card" : "student-card";
  };

  const getIconWrapperClass = () => {
    return user?.role === "doctor"
      ? "doctor-icon-wrapper"
      : "student-icon-wrapper";
  };

  const getGradientTextClass = () => {
    return user?.role === "doctor"
      ? "doctor-gradient-text"
      : "student-gradient-text";
  };

  const getIconColor = () => {
    return user?.role === "doctor" ? "text-green-600" : "text-blue-600";
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div className={`${getCardClass()} rounded-2xl p-6`}>
        <div className="flex items-center space-x-4 mb-6">
          <div className={getIconWrapperClass()}>
            <FiClock className={`text-2xl ${getIconColor()}`} />
          </div>
          <div>
            <h1 className={`text-3xl font-bold ${getGradientTextClass()}`}>
              TA Attendance Board
            </h1>
            <p className="text-gray-600">
              {user?.role === "doctor"
                ? "Manage your availability and schedule"
                : "View teaching assistant availability and schedules"}
            </p>
          </div>
        </div>

        {user?.role === "doctor" ? (
          // Doctor View - Form to manage availability
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Update Your Availability
            </h2>

            {/* Status Toggle */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <button
                onClick={handleStatusToggle}
                className={`flex items-center space-x-3 px-4 py-3 rounded-xl border-2 transition-all duration-300 hover:shadow-lg ${
                  availability.status === "available"
                    ? "border-green-300 bg-green-50 text-green-700"
                    : "border-red-300 bg-red-50 text-red-700"
                }`}
              >
                {availability.status === "available" ? (
                  <FiUserCheck className="text-xl" />
                ) : (
                  <FiUserX className="text-xl" />
                )}
                <span className="font-medium">
                  {availability.status === "available"
                    ? "Available"
                    : "Not Available"}
                </span>
              </button>
            </div>

            {/* Location Input */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                <FiMapPin className="inline mr-2" />
                Location
              </label>
              <input
                type="text"
                name="location"
                value={availability.location}
                onChange={handleInputChange}
                placeholder="Enter your location (e.g., Office 201, Lab A)"
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300"
              />
            </div>

            {/* Available From Time */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                <FiClock className="inline mr-2" />
                Available From
              </label>
              <input
                type="time"
                name="availableFrom"
                value={availability.availableFrom}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300"
              />
            </div>

            {/* Available To Time */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                <FiClock className="inline mr-2" />
                Available To
              </label>
              <input
                type="time"
                name="availableTo"
                value={availability.availableTo}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300"
              />
            </div>

            {/* Save Button */}
            <motion.button
              onClick={handleSaveAvailability}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 px-6 rounded-xl font-semibold flex items-center justify-center space-x-2 hover:from-green-700 hover:to-green-800 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <FiSave className="text-lg" />
              <span>Save / Update Availability</span>
            </motion.button>
          </motion.div>
        ) : (
          // Student View - Display doctor's availability
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            {doctorAvailability ? (
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
                <h2 className="text-xl font-semibold text-gray-800 mb-6">
                  Doctor's Current Availability
                </h2>

                {/* Status Display */}
                <div className="flex items-center space-x-4 mb-4">
                  <div
                    className={`flex items-center space-x-2 px-4 py-2 rounded-xl ${
                      doctorAvailability.status === "available"
                        ? "bg-green-100 text-green-700"
                        : "bg-red-100 text-red-700"
                    }`}
                  >
                    {doctorAvailability.status === "available" ? (
                      <FiUserCheck className="text-lg" />
                    ) : (
                      <FiUserX className="text-lg" />
                    )}
                    <span className="font-medium">
                      {doctorAvailability.status === "available"
                        ? "Available"
                        : "Not Available"}
                    </span>
                  </div>
                </div>

                {/* Location Display */}
                {doctorAvailability.location && (
                  <div className="flex items-center space-x-3 mb-4">
                    <FiMapPin className="text-blue-600 text-lg" />
                    <div>
                      <span className="text-sm text-gray-600">Location:</span>
                      <p className="font-medium text-gray-800">
                        {doctorAvailability.location}
                      </p>
                    </div>
                  </div>
                )}

                {/* Time Range Display */}
                {doctorAvailability.availableFrom &&
                  doctorAvailability.availableTo && (
                    <div className="flex items-center space-x-3">
                      <FiClock className="text-blue-600 text-lg" />
                      <div>
                        <span className="text-sm text-gray-600">
                          Available Time:
                        </span>
                        <p className="font-medium text-gray-800">
                          {doctorAvailability.availableFrom} -{" "}
                          {doctorAvailability.availableTo}
                        </p>
                      </div>
                    </div>
                  )}
              </div>
            ) : (
              // No availability data message
              <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-2xl p-6 border border-yellow-200 text-center">
                <FiAlertCircle className="text-4xl text-yellow-600 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-gray-800 mb-2">
                  No Availability Information
                </h2>
                <p className="text-gray-600">
                  Doctor has not updated availability yet. Please check back
                  later.
                </p>
              </div>
            )}
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default TAAttendance;
