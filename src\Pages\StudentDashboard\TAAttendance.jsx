import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON>,
  FiUserCheck,
  FiUserX,
  FiMapPin,
  FiSave,
  FiAlertCircle,
  FiSearch,
  FiUser,
} from "react-icons/fi";
import { useAuth } from "../../contexts/AuthContext";
import Swal from "sweetalert2";

const TAAttendance = () => {
  const { user } = useAuth();
  const [availability, setAvailability] = useState({
    status: "available",
    location: "",
    availableFrom: "",
    availableTo: "",
    doctorName: "",
  });
  const [doctorAvailability, setDoctorAvailability] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [allDoctorsAvailability, setAllDoctorsAvailability] = useState([]);

  // Load data from localStorage on component mount
  useEffect(() => {
    if (user?.role === "doctor") {
      // Load doctor's saved availability
      const savedAvailability = localStorage.getItem(
        `doctorAvailability_${user.collegeId}`
      );
      if (savedAvailability) {
        setAvailability(JSON.parse(savedAvailability));
      } else {
        // Set doctor name from user data if no saved data
        setAvailability((prev) => ({
          ...prev,
          doctorName: `${user.firstName} ${user.lastName}` || "Dr. Unknown",
        }));
      }
    } else {
      // Load all doctors' availability for student view
      const allDoctors = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith("doctorAvailability_")) {
          const doctorData = localStorage.getItem(key);
          if (doctorData) {
            allDoctors.push(JSON.parse(doctorData));
          }
        }
      }
      setAllDoctorsAvailability(allDoctors);

      // Set the first doctor as default if available
      if (allDoctors.length > 0) {
        setDoctorAvailability(allDoctors[0]);
      }
    }
  }, [user]);

  // Handle form input changes for doctor
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setAvailability((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle status toggle for doctor
  const handleStatusToggle = () => {
    setAvailability((prev) => ({
      ...prev,
      status: prev.status === "available" ? "not-available" : "available",
    }));
  };

  // Save availability data for doctor
  const handleSaveAvailability = () => {
    // Add doctor name and ID to availability data
    const availabilityWithDoctor = {
      ...availability,
      doctorName: `${user.firstName} ${user.lastName}` || "Dr. Unknown",
      doctorId: user.collegeId,
      lastUpdated: new Date().toISOString(),
    };

    localStorage.setItem(
      `doctorAvailability_${user.collegeId}`,
      JSON.stringify(availabilityWithDoctor)
    );

    // Show success message with SweetAlert
    Swal.fire({
      icon: "success",
      title: "تم التحديث بنجاح!",
      text: "تم حفظ بيانات التواجد بنجاح",
      confirmButtonText: "موافق",
      confirmButtonColor: "#22c55e",
      timer: 3000,
      timerProgressBar: true,
    });
  };

  // Get card and styling classes based on role
  const getCardClass = () => {
    return user?.role === "doctor" ? "doctor-card" : "student-card";
  };

  const getIconWrapperClass = () => {
    return user?.role === "doctor"
      ? "doctor-icon-wrapper"
      : "student-icon-wrapper";
  };

  const getGradientTextClass = () => {
    return user?.role === "doctor"
      ? "doctor-gradient-text"
      : "student-gradient-text";
  };

  const getIconColor = () => {
    return user?.role === "doctor" ? "text-green-600" : "text-blue-600";
  };

  // Filter doctors based on search term
  const filteredDoctors = allDoctorsAvailability.filter((doctor) =>
    doctor.doctorName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // Handle doctor selection from search results
  const handleDoctorSelect = (doctor) => {
    setDoctorAvailability(doctor);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div className={`${getCardClass()} rounded-2xl p-6`}>
        <div className="flex items-center space-x-4 mb-6">
          <div className={getIconWrapperClass()}>
            <FiClock className={`text-2xl ${getIconColor()}`} />
          </div>
          <div>
            <h1 className={`text-3xl font-bold ${getGradientTextClass()}`}>
              TA Attendance Board
            </h1>
            <p className="text-gray-600">
              {user?.role === "doctor"
                ? "Manage your availability and schedule"
                : "View teaching assistant availability and schedules"}
            </p>
          </div>
        </div>

        {user?.role === "doctor" ? (
          // Doctor View - Form to manage availability
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Update Your Availability
            </h2>

            {/* Doctor Name Input */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                <FiUser className="inline mr-2" />
                Doctor Name
              </label>
              <input
                type="text"
                name="doctorName"
                value={availability.doctorName}
                onChange={handleInputChange}
                placeholder="Enter your name"
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300"
              />
            </div>

            {/* Status Toggle */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <button
                onClick={handleStatusToggle}
                className={`flex items-center space-x-3 px-4 py-3 rounded-xl border-2 transition-all duration-300 hover:shadow-lg ${
                  availability.status === "available"
                    ? "border-green-300 bg-green-50 text-green-700"
                    : "border-red-300 bg-red-50 text-red-700"
                }`}
              >
                {availability.status === "available" ? (
                  <FiUserCheck className="text-xl" />
                ) : (
                  <FiUserX className="text-xl" />
                )}
                <span className="font-medium">
                  {availability.status === "available"
                    ? "Available"
                    : "Not Available"}
                </span>
              </button>
            </div>

            {/* Location Input */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                <FiMapPin className="inline mr-2" />
                Location
              </label>
              <input
                type="text"
                name="location"
                value={availability.location}
                onChange={handleInputChange}
                placeholder="Enter your location (e.g., Office 201, Lab A)"
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300"
              />
            </div>

            {/* Available From Time */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                <FiClock className="inline mr-2" />
                Available From
              </label>
              <input
                type="time"
                name="availableFrom"
                value={availability.availableFrom}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300"
              />
            </div>

            {/* Available To Time */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                <FiClock className="inline mr-2" />
                Available To
              </label>
              <input
                type="time"
                name="availableTo"
                value={availability.availableTo}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300"
              />
            </div>

            {/* Save Button */}
            <motion.button
              onClick={handleSaveAvailability}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 px-6 rounded-xl font-semibold flex items-center justify-center space-x-2 hover:from-green-700 hover:to-green-800 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <FiSave className="text-lg" />
              <span>Save / Update Availability</span>
            </motion.button>
          </motion.div>
        ) : (
          // Student View - Display doctor's availability
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            {/* Search Section */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                <FiSearch className="inline mr-2" />
                Search for Doctor
              </h2>

              {/* Search Input */}
              <div className="relative mb-4">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Search by doctor name..."
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
                />
              </div>

              {/* Search Results */}
              {searchTerm && filteredDoctors.length > 0 && (
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {filteredDoctors.map((doctor, index) => (
                    <motion.button
                      key={index}
                      onClick={() => handleDoctorSelect(doctor)}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="w-full text-left p-3 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200"
                    >
                      <div className="flex items-center space-x-3">
                        <FiUser className="text-blue-600" />
                        <div>
                          <p className="font-medium text-gray-800">
                            {doctor.doctorName}
                          </p>
                          <p className="text-sm text-gray-600">
                            Status:{" "}
                            {doctor.status === "available"
                              ? "Available"
                              : "Not Available"}
                          </p>
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              )}

              {/* No Results */}
              {searchTerm && filteredDoctors.length === 0 && (
                <div className="text-center py-4">
                  <FiAlertCircle className="text-2xl text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">
                    No doctors found matching your search.
                  </p>
                </div>
              )}
            </div>

            {/* Selected Doctor's Availability */}
            {doctorAvailability ? (
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
                <h2 className="text-xl font-semibold text-gray-800 mb-6">
                  {doctorAvailability.doctorName}'s Current Availability
                </h2>

                {/* Doctor Name Display */}
                <div className="flex items-center space-x-3 mb-4 p-3 bg-white rounded-lg border border-blue-200">
                  <FiUser className="text-blue-600 text-lg" />
                  <div>
                    <span className="text-sm text-gray-600">Doctor:</span>
                    <p className="font-medium text-gray-800 text-lg">
                      {doctorAvailability.doctorName}
                    </p>
                  </div>
                </div>

                {/* Status Display */}
                <div className="flex items-center space-x-4 mb-4">
                  <div
                    className={`flex items-center space-x-2 px-4 py-2 rounded-xl ${
                      doctorAvailability.status === "available"
                        ? "bg-green-100 text-green-700"
                        : "bg-red-100 text-red-700"
                    }`}
                  >
                    {doctorAvailability.status === "available" ? (
                      <FiUserCheck className="text-lg" />
                    ) : (
                      <FiUserX className="text-lg" />
                    )}
                    <span className="font-medium">
                      {doctorAvailability.status === "available"
                        ? "Available"
                        : "Not Available"}
                    </span>
                  </div>
                </div>

                {/* Location Display */}
                {doctorAvailability.location && (
                  <div className="flex items-center space-x-3 mb-4">
                    <FiMapPin className="text-blue-600 text-lg" />
                    <div>
                      <span className="text-sm text-gray-600">Location:</span>
                      <p className="font-medium text-gray-800">
                        {doctorAvailability.location}
                      </p>
                    </div>
                  </div>
                )}

                {/* Time Range Display */}
                {doctorAvailability.availableFrom &&
                  doctorAvailability.availableTo && (
                    <div className="flex items-center space-x-3">
                      <FiClock className="text-blue-600 text-lg" />
                      <div>
                        <span className="text-sm text-gray-600">
                          Available Time:
                        </span>
                        <p className="font-medium text-gray-800">
                          {doctorAvailability.availableFrom} -{" "}
                          {doctorAvailability.availableTo}
                        </p>
                      </div>
                    </div>
                  )}

                {/* Last Updated */}
                {doctorAvailability.lastUpdated && (
                  <div className="mt-4 pt-4 border-t border-blue-200">
                    <p className="text-sm text-gray-500">
                      Last updated:{" "}
                      {new Date(doctorAvailability.lastUpdated).toLocaleString(
                        "ar-EG"
                      )}
                    </p>
                  </div>
                )}
              </div>
            ) : (
              // No availability data message
              <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-2xl p-6 border border-yellow-200 text-center">
                <FiAlertCircle className="text-4xl text-yellow-600 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-gray-800 mb-2">
                  No Doctor Selected
                </h2>
                <p className="text-gray-600">
                  Please search and select a doctor to view their availability.
                </p>
              </div>
            )}

            {/* All Available Doctors */}
            {allDoctorsAvailability.length > 0 && (
              <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">
                  All Available Doctors ({allDoctorsAvailability.length})
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {allDoctorsAvailability.map((doctor, index) => (
                    <motion.div
                      key={index}
                      whileHover={{ scale: 1.02 }}
                      className="bg-white rounded-lg p-4 border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all duration-200 cursor-pointer"
                      onClick={() => handleDoctorSelect(doctor)}
                    >
                      <div className="flex items-center space-x-3 mb-2">
                        <FiUser className="text-blue-600" />
                        <h3 className="font-medium text-gray-800">
                          {doctor.doctorName}
                        </h3>
                      </div>
                      <div className="flex items-center space-x-2 mb-2">
                        {doctor.status === "available" ? (
                          <FiUserCheck className="text-green-600 text-sm" />
                        ) : (
                          <FiUserX className="text-red-600 text-sm" />
                        )}
                        <span
                          className={`text-sm font-medium ${
                            doctor.status === "available"
                              ? "text-green-600"
                              : "text-red-600"
                          }`}
                        >
                          {doctor.status === "available"
                            ? "Available"
                            : "Not Available"}
                        </span>
                      </div>
                      {doctor.location && (
                        <div className="flex items-center space-x-2 mb-2">
                          <FiMapPin className="text-gray-500 text-sm" />
                          <span className="text-sm text-gray-600">
                            {doctor.location}
                          </span>
                        </div>
                      )}
                      {doctor.availableFrom && doctor.availableTo && (
                        <div className="flex items-center space-x-2">
                          <FiClock className="text-gray-500 text-sm" />
                          <span className="text-sm text-gray-600">
                            {doctor.availableFrom} - {doctor.availableTo}
                          </span>
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {/* No Doctors Available */}
            {allDoctorsAvailability.length === 0 && (
              <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200 text-center">
                <FiAlertCircle className="text-4xl text-gray-400 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-gray-800 mb-2">
                  No Doctors Available
                </h2>
                <p className="text-gray-600">
                  No doctors have updated their availability yet. Please check
                  back later.
                </p>
              </div>
            )}
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default TAAttendance;
