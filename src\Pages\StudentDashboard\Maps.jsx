import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  FiMap,
  FiBook,
  FiUsers,
  FiCoffee,
  FiBookOpen,
  FiCalendar,
  FiMapPin,
  FiArrowRight,
} from "react-icons/fi";

const Maps = () => {
  const [activeTab, setActiveTab] = useState("lectures");

  const tabs = [
    { id: "lectures", label: "مبنى المحاضرات", icon: FiBook },
    { id: "sections", label: "مبنى السكاشن", icon: FiUsers },
    { id: "other", label: "أماكن أخرى", icon: FiMapPin },
  ];

  const buildingData = {
    lectures: {
      title: "مبنى المحاضرات",
      floors: [
        {
          floor: "الدور الأول",
          description: "قاعات المحاضرات الكبيرة (101-110) ومكتب شؤون الطلاب",
          location: "عند المدخل الرئيسي، اتجه يميناً مباشرة",
        },
        {
          floor: "الدور الثاني",
          description:
            "قاعات المحاضرات المتوسطة (201-215) ومكاتب أعضاء هيئة التدريس",
          location: "اصعد السلم الرئيسي، ستجد القاعات على اليمين واليسار",
        },
        {
          floor: "الدور الثالث",
          description: "معامل الحاسوب (301-308) وقاعة الاجتماعات الكبرى",
          location:
            "اصعد للدور الثالث، المعامل في المقدمة وقاعة الاجتماعات في النهاية",
        },
        {
          floor: "الدور الرابع",
          description: "مكاتب إدارية ومكتب العميد وقاعة مجلس الكلية",
          location:
            "الدور الأخير، مكتب العميد في المنتصف مع إطلالة على الحرم الجامعي",
        },
      ],
    },
    sections: {
      title: "مبنى السكاشن",
      floors: [
        {
          floor: "الدور الأول",
          description: "قاعات السكاشن الصغيرة (S101-S120) ومعمل الفيزياء",
          location: "المبنى الجانبي، ادخل من البوابة الشرقية واتجه يساراً",
        },
        {
          floor: "الدور الثاني",
          description: "قاعات السكاشن المتخصصة (S201-S218) ومعمل الكيمياء",
          location: "اصعد السلم، ستجد معمل الكيمياء في بداية الممر",
        },
        {
          floor: "الدور الثالث",
          description: "استوديوهات التصميم (S301-S310) ومعمل الجرافيك",
          location: "الدور مخصص للتصميم، الاستوديوهات مجهزة بأحدث الأجهزة",
        },
        {
          floor: "الدور الرابع",
          description: "قاعات المناقشات الجماعية ومكتبة الأبحاث الصغيرة",
          location: "دور هادئ للدراسة والمناقشات، إطلالة جميلة على الحديقة",
        },
      ],
    },
    other: {
      title: "أماكن أخرى",
      places: [
        {
          name: "الكافيتريا",
          description: "مكان لتناول الطعام والمشروبات مع جلسات مريحة",
          location: "في وسط الحرم الجامعي، بجانب النافورة الرئيسية",
          icon: FiCoffee,
        },
        {
          name: "مكتبة الكلية",
          description: "مكتبة شاملة تحتوي على آلاف الكتب والمراجع العلمية",
          location: "المبنى الرئيسي، الدور الأرضي، بجانب قاعة الاستقبال",
          icon: FiBookOpen,
        },
        {
          name: "قاعة الفعاليات",
          description: "قاعة كبيرة للمؤتمرات والفعاليات والحفلات الجامعية",
          location: "المبنى الخلفي، مدخل منفصل من الجهة الغربية للحرم",
          icon: FiCalendar,
        },
      ],
    },
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="student-card rounded-2xl p-6">
        <div className="flex items-center space-x-4 mb-6">
          <div className="student-icon-wrapper">
            <FiMap className="text-2xl text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold student-gradient-text">
              دليل أماكن الكلية
            </h1>
            <p className="text-gray-600">
              تعرف على جميع أماكن ومباني كلية الحاسبات والمعلومات
            </p>
          </div>
        </div>

        {/* Google Maps Embed */}
        <div className="mb-8 rounded-xl overflow-hidden shadow-lg">
          <iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3434.671718444566!2d31.527055924749945!3d30.58681139267076!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14f7f125ba86a5d3%3A0xb34e3748fa9ee184!2z2YPZhNmK2Kkg2KfZhNit2KfYs9io2KfYqiDZiNin2YTZhdi52YTZiNmF2KfYqiDYrNin2YXYudipINin2YTYstmC2KfYstmK2YI!5e0!3m2!1sar!2seg!4v1755863588087!5m2!1sar!2seg"
            width="100%"
            height="400"
            style={{ border: 0 }}
            allowFullScreen=""
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
            className="w-full"
          />
        </div>

        {/* Tabs */}
        <div className="flex flex-wrap gap-2 mb-6 border-b border-gray-200">
          {tabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-3 rounded-t-lg transition-all duration-300 ${
                  activeTab === tab.id
                    ? "bg-blue-50 text-blue-600 border-b-2 border-blue-600"
                    : "text-gray-600 hover:text-blue-600 hover:bg-gray-50"
                }`}
              >
                <IconComponent className="text-lg" />
                <span className="font-medium">{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-4"
        >
          {(activeTab === "lectures" || activeTab === "sections") && (
            <div>
              <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                <span className="ml-2">{buildingData[activeTab].title}</span>
              </h2>
              <div className="grid gap-4">
                {buildingData[activeTab].floors.map((floor, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start space-x-4">
                      <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold text-sm">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-blue-800 mb-2">
                          {floor.floor}
                        </h3>
                        <p className="text-gray-700 mb-3 leading-relaxed">
                          {floor.description}
                        </p>
                        <div className="flex items-center text-blue-600">
                          <FiMapPin className="ml-2" />
                          <span className="text-sm font-medium">
                            {floor.location}
                          </span>
                          <FiArrowRight className="mr-2" />
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {activeTab === "other" && (
            <div>
              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                {buildingData.other.title}
              </h2>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {buildingData.other.places.map((place, index) => {
                  const IconComponent = place.icon;
                  return (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                    >
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="bg-green-600 text-white rounded-full p-3">
                          <IconComponent className="text-xl" />
                        </div>
                        <h3 className="text-xl font-bold text-green-800">
                          {place.name}
                        </h3>
                      </div>
                      <p className="text-gray-700 mb-4 leading-relaxed">
                        {place.description}
                      </p>
                      <div className="flex items-center text-green-600">
                        <FiMapPin className="ml-2" />
                        <span className="text-sm font-medium">
                          {place.location}
                        </span>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default Maps;
