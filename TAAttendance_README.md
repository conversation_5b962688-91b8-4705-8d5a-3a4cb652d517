# TAAttendance Component

A React component that provides role-based functionality for managing and viewing Teaching Assistant (TA) availability.

## Features

### Role Detection

- Automatically detects user role from AuthContext
- Supports "doctor" and "student" roles
- Fallback to "student" role if no role is detected

### Doctor View (Management Interface)

- **Doctor Name Input**: Text field for entering doctor's name with FiUser icon
- **Status Toggle**: Interactive button to toggle between "Available" and "Not Available"
  - Green styling with FiUserCheck icon for Available
  - Red styling with FiUserX icon for Not Available
- **Location Input**: Text field for entering office/location information with FiMapPin icon
- **Time Range**: Two time pickers for "Available From" and "Available To" with FiClock icons
- **Save Button**: Saves all data to localStorage with unique doctor ID key
- **Success Feedback**: SweetAlert notification in Arabic when data is saved
- **Auto-populate**: Doctor name auto-populated from user profile data

### Student View (Read-Only Display)

- **Search Functionality**: Real-time search for doctors by name with FiSearch icon
- **Search Results**: Dropdown list of matching doctors with quick selection
- **Doctor Selection**: Click to select and view specific doctor's availability
- **Availability Card**: Displays selected doctor's current availability in a styled card
- **Doctor Name Display**: Prominently shows doctor's name in the availability card
- **Status Display**: Shows availability status with appropriate icon and color
- **Location Display**: Shows doctor's location with map pin icon
- **Time Range Display**: Shows available time range with clock icons
- **Last Updated**: Shows when the availability was last updated
- **All Doctors Grid**: Grid view of all available doctors with quick overview
- **No Data States**:
  - No doctor selected message
  - No search results found
  - No doctors available message

## Styling

### Role-Based Styling

- **Student**: Blue color scheme (blue-600, blue-700)
- **Doctor**: Green color scheme (green-600, green-700)

### CSS Classes Used

- `student-card`, `student-icon-wrapper`, `student-gradient-text`
- `doctor-card`, `doctor-icon-wrapper`, `doctor-gradient-text`

### Animations

- Framer Motion animations for smooth transitions
- Hover and tap animations on interactive elements
- Staggered animations for form elements

## Data Storage

### localStorage Keys

- `doctorAvailability_{doctorId}`: Stores individual doctor's availability data
  ```json
  {
    "status": "available" | "not-available",
    "location": "string",
    "availableFrom": "HH:MM",
    "availableTo": "HH:MM",
    "doctorName": "string",
    "doctorId": "string",
    "lastUpdated": "ISO date string"
  }
  ```

## Icons Used

- `FiClock`: Main component icon and time fields
- `FiUserCheck`: Available status
- `FiUserX`: Not available status
- `FiMapPin`: Location field and display
- `FiSave`: Save button
- `FiAlertCircle`: No data message and alerts
- `FiSearch`: Search functionality
- `FiUser`: Doctor name field and display

## Usage

The component is automatically integrated into both Student and Doctor dashboards:

### Student Dashboard

- Route: `/student/ta-attendance`
- Shows read-only view of doctor's availability

### Doctor Dashboard

- Route: `/doctor/ta-attendance`
- Shows management interface for updating availability

## Dependencies

- React (useState, useEffect)
- Framer Motion
- React Icons (Fi icons)
- AuthContext for user role detection
- SweetAlert2 for notifications

## File Structure

```
src/Pages/StudentDashboard/TAAttendance.jsx  # Main component
src/Pages/DoctorDashboard/TAAttendance.jsx   # Re-exports main component
src/index.css                               # Contains role-based CSS classes
```

## New Features Added

### Enhanced Doctor Management

- **Unique Doctor IDs**: Each doctor's data is stored with their unique college ID
- **Doctor Name Display**: Doctor's name is prominently displayed in all views
- **Auto-population**: Doctor name automatically filled from user profile
- **Last Updated Timestamp**: Shows when availability was last modified

### Advanced Student Search

- **Real-time Search**: Search doctors by name with instant results
- **Search Results Dropdown**: Interactive list of matching doctors
- **Quick Selection**: Click any doctor from search results to view their availability
- **All Doctors Overview**: Grid view showing all available doctors at once
- **Multiple Data States**: Proper handling of no data, no results, and no selection states

### Improved User Experience

- **SweetAlert Integration**: Beautiful Arabic notifications instead of basic alerts
- **Enhanced Visual Feedback**: Better status indicators and hover effects
- **Responsive Design**: Grid layout adapts to different screen sizes
- **Smooth Animations**: Enhanced Framer Motion animations throughout

## Testing

To test the component:

1. **As Doctor**:

   - Login with doctor credentials
   - Navigate to TA Attendance Board
   - Fill out availability form (name auto-populated)
   - Toggle status and set location/times
   - Save and verify SweetAlert success message
   - Verify data persists on page refresh

2. **As Student**:

   - Login with student credentials
   - Navigate to TA Attendance Board
   - Use search to find specific doctors
   - Click on search results to select doctors
   - View detailed availability information
   - Browse all doctors in grid view
   - Test various no-data states

3. **Multi-Doctor Testing**:
   - Create multiple doctor accounts
   - Have each doctor set different availability
   - Test search functionality with multiple results
   - Verify unique storage per doctor ID

## Browser Compatibility

- Modern browsers with localStorage support
- CSS Grid and Flexbox support required
- ES6+ JavaScript features used
