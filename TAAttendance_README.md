# TAAttendance Component

A React component that provides role-based functionality for managing and viewing Teaching Assistant (TA) availability.

## Features

### Role Detection
- Automatically detects user role from AuthContext
- Supports "doctor" and "student" roles
- Fallback to "student" role if no role is detected

### Doctor View (Management Interface)
- **Status Toggle**: Interactive button to toggle between "Available" and "Not Available"
  - Green styling with FiUserCheck icon for Available
  - Red styling with FiUserX icon for Not Available
- **Location Input**: Text field for entering office/location information
- **Time Range**: Two time pickers for "Available From" and "Available To"
- **Save Button**: Saves all data to localStorage under "doctorAvailability" key
- **Success Feedback**: Alert notification when data is saved

### Student View (Read-Only Display)
- **Availability Card**: Displays doctor's current availability in a styled card
- **Status Display**: Shows availability status with appropriate icon and color
- **Location Display**: Shows doctor's location with map pin icon
- **Time Range Display**: Shows available time range with clock icons
- **No Data State**: Shows informative message when no availability data exists

## Styling

### Role-Based Styling
- **Student**: Blue color scheme (blue-600, blue-700)
- **Doctor**: Green color scheme (green-600, green-700)

### CSS Classes Used
- `student-card`, `student-icon-wrapper`, `student-gradient-text`
- `doctor-card`, `doctor-icon-wrapper`, `doctor-gradient-text`

### Animations
- Framer Motion animations for smooth transitions
- Hover and tap animations on interactive elements
- Staggered animations for form elements

## Data Storage

### localStorage Keys
- `doctorAvailability`: Stores doctor's availability data
  ```json
  {
    "status": "available" | "not-available",
    "location": "string",
    "availableFrom": "HH:MM",
    "availableTo": "HH:MM"
  }
  ```

## Icons Used
- `FiClock`: Main component icon and time fields
- `FiUserCheck`: Available status
- `FiUserX`: Not available status
- `FiMapPin`: Location field and display
- `FiSave`: Save button
- `FiAlertCircle`: No data message

## Usage

The component is automatically integrated into both Student and Doctor dashboards:

### Student Dashboard
- Route: `/student/ta-attendance`
- Shows read-only view of doctor's availability

### Doctor Dashboard
- Route: `/doctor/ta-attendance`
- Shows management interface for updating availability

## Dependencies
- React (useState, useEffect)
- Framer Motion
- React Icons (Fi icons)
- AuthContext for user role detection

## File Structure
```
src/Pages/StudentDashboard/TAAttendance.jsx  # Main component
src/Pages/DoctorDashboard/TAAttendance.jsx   # Re-exports main component
src/index.css                               # Contains role-based CSS classes
```

## Testing

To test the component:

1. **As Doctor**:
   - Login with doctor credentials
   - Navigate to TA Attendance Board
   - Fill out availability form and save
   - Verify data persists on page refresh

2. **As Student**:
   - Login with student credentials
   - Navigate to TA Attendance Board
   - Verify doctor's availability is displayed correctly
   - Test no-data state by clearing localStorage

## Browser Compatibility
- Modern browsers with localStorage support
- CSS Grid and Flexbox support required
- ES6+ JavaScript features used
