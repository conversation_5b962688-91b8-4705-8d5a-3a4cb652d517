@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  background-color: #f8fafc;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-corner {
  background: transparent;
  border-radius: 100px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  border-radius: 10px;
  border: 1px solid #e2e8f0;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #475569 0%, #334155 100%);
}

/* Smooth transitions for all elements */
* {
  transition: color 0.2s ease, background-color 0.2s ease,
    border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

/* Focus styles */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  ring-opacity: 0.5;
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Gradient backgrounds */
.gradient-bg-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.gradient-bg-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.gradient-bg-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

/* Text gradients */
.text-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Card styles */
.glass-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
}

/* Button styles */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Cosmic Animations */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes cosmic-pulse {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes cosmic-glow {
  0%,
  100% {
    box-shadow: 0 0 20px currentColor;
  }
  50% {
    box-shadow: 0 0 40px currentColor, 0 0 60px currentColor;
  }
}

@keyframes cosmic-float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

@keyframes cosmic-sparkle {
  0%,
  100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
}

@keyframes energy-flow {
  0% {
    stroke-dashoffset: 100;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

/* Cosmic utility classes */
.cosmic-glow {
  animation: cosmic-glow 2s ease-in-out infinite;
}

.cosmic-pulse {
  animation: cosmic-pulse 3s ease-in-out infinite;
}

.cosmic-float {
  animation: cosmic-float 6s ease-in-out infinite;
}

.cosmic-sparkle {
  animation: cosmic-sparkle 2s ease-in-out infinite;
}

.gradient-shift {
  animation: gradient-shift 3s ease infinite;
}

/* Cosmic scrollbar */
.cosmic-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.cosmic-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

.cosmic-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
  border-radius: 10px;
  box-shadow: 0 0 10px #00ffff;
}

.cosmic-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #ff00ff 0%, #00ffff 100%);
  box-shadow: 0 0 20px #ff00ff;
}

/* Cosmic text effects */
.cosmic-text {
  background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ff00);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease infinite;
}

.neon-glow {
  text-shadow: 0 0 5px currentColor, 0 0 10px currentColor,
    0 0 15px currentColor, 0 0 20px currentColor;
}

/* Role-based card styles */
.student-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.1);
}

.student-icon-wrapper {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  padding: 0.75rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.student-gradient-text {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.doctor-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(34, 197, 94, 0.2);
  box-shadow: 0 8px 32px rgba(34, 197, 94, 0.1);
}

.doctor-icon-wrapper {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  padding: 0.75rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.doctor-gradient-text {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.affairs-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(147, 51, 234, 0.2);
  box-shadow: 0 8px 32px rgba(147, 51, 234, 0.1);
}

.affairs-icon-wrapper {
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
  padding: 0.75rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.affairs-gradient-text {
  background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
