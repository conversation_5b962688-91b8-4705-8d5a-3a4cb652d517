import React from "react";
import { Routes, Route } from "react-router-dom";

// Import page components
import TeacherPage from "./TeacherPage";
import LearningResources from "./LearningResources";
import TAAttendance from "./TAAttendance";
import AbsenceSystem from "./AbsenceSystem";

const DoctorDashboard = () => {
  return (
    <div className="doctor-dashboard">
      <Routes>
        <Route path="/" element={<DashboardHome />} />
        <Route path="/teacher-page" element={<TeacherPage />} />
        <Route path="/learning-resources" element={<LearningResources />} />
        <Route path="/ta-attendance" element={<TAAttendance />} />
        <Route path="/absence-system" element={<AbsenceSystem />} />
      </Routes>
    </div>
  );

  function DashboardHome() {
    return (
      <>
        <h1>Doctor Dashboard</h1>
      </>
    );
  }
};

export default DoctorDashboard;
